.kc-web-sdk-btn {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  font-weight: 600 !important;
  display: inline-block !important;
  cursor: pointer;
  border-radius: 4px !important;
  background: #2F92E5 !important;
  color: #ffffff !important;
  margin: 0px;
  padding: 0px;
  -webkit-box-sizing: border-box !important;
  box-sizing: border-box !important;
  min-width: 31ch !important;
  min-height: 2.5em !important;
  position: relative !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.kc-web-sdk-btn::before {
  background-image: url("//kingsch.at/h/css/images/kc-logo.png") !important;
  background-position: left !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  height: 1.4em !important;
  line-height: 1.4em !important;
  content: "LOGIN WITH KINGSCHAT" !important;
  white-space: nowrap !important;
  padding-left: 2.3em !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  -webkit-transform: translate(-50%, -50%) !important;
  -ms-transform: translate(-50%, -50%) !important;
  transform: translate(-50%, -50%) !important;
}

.kc-web-sdk-btn-m {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  font-weight: 600 !important;
  display: inline-block !important;
  cursor: pointer;
  border-radius: 4px !important;
  background: #2F92E5 !important;
  color: #ffffff !important;
  margin: 0px;
  padding: 0px;
  -webkit-box-sizing: border-box !important;
  box-sizing: border-box !important;
  min-width: 13ch !important;
  min-height: 2.2em !important;
  position: relative !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.kc-web-sdk-btn-m::before {
  background-image: url("//kingsch.at/h/css/images/kc-logo.png") !important;
  background-position: left !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  height: 1.3em !important;
  line-height: 1.3em !important;
  content: "LOGIN" !important;
  white-space: nowrap !important;
  padding-left: 2em !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  -webkit-transform: translate(-50%, -50%) !important;
  -ms-transform: translate(-50%, -50%) !important;
  transform: translate(-50%, -50%) !important;
}

.kc-web-sdk-btn-s {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  font-weight: 600 !important;
  display: inline-block !important;
  cursor: pointer;
  border-radius: 4px !important;
  background: #2F92E5 !important;
  color: #ffffff !important;
  margin: 0px;
  padding: 0px;
  -webkit-box-sizing: border-box !important;
  box-sizing: border-box !important;
  min-width: 10ch !important;
  min-height: 1.5em !important;
  position: relative !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.kc-web-sdk-btn-s::before {
  background-image: url("//kingsch.at/h/css/images/kc-logo.png") !important;
  background-position: left !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  height: 1.1em !important;
  line-height: 1.1em !important;
  font-size: 0.85em;
  content: "LOGIN" !important;
  white-space: nowrap !important;
  padding-left: 1.6em !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  -webkit-transform: translate(-50%, -50%) !important;
  -ms-transform: translate(-50%, -50%) !important;
  transform: translate(-50%, -50%) !important;
}
