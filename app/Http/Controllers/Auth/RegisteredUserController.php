<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'user',
        ]);

        event(new Registered($user));

        Auth::login($user);

        // Check for pending KingsChat connection
        if (session()->has('kingschat_pending_connection')) {
            $kingsChatData = session('kingschat_pending_connection');

            // Connect KingsChat
            $user->connectKingsChat(
                $kingsChatData['user_id'],
                $kingsChatData['access_token'],
                $kingsChatData['refresh_token'] ?? '',
                $kingsChatData['expires_in'] ?? 3600
            );

            // Send welcome message
            $kingsChatService = app(\App\Services\KingsChatService::class);
            $kingsChatService->sendMessageWithToken(
                $kingsChatData['user_id'],
                "🎉 Welcome to GPDScheduler, {$user->name}! Your account has been created and KingsChat connected successfully.",
                $kingsChatData['access_token']
            );

            // Clear the pending connection
            session()->forget('kingschat_pending_connection');

            return redirect(route('dashboard', absolute: false))
                ->with('success', 'Account created and KingsChat connected successfully!');
        }

        return redirect(route('dashboard', absolute: false));
    }
}
