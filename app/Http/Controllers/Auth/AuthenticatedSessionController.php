<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Check for pending KingsChat connection
        if (session()->has('kingschat_pending_connection')) {
            $kingsChatData = session('kingschat_pending_connection');
            $user = Auth::user();

            // Connect KingsChat
            $user->connectKingsChat(
                $kingsChatData['user_id'],
                $kingsChatData['access_token'],
                $kingsChatData['refresh_token'] ?? '',
                $kingsChatData['expires_in'] ?? 3600
            );

            // Send welcome message
            $kingsChatService = app(\App\Services\KingsChatService::class);
            $kingsChatService->sendMessageWithToken(
                $kingsChatData['user_id'],
                "🎉 Welcome to GPDScheduler, {$user->name}! Your KingsChat account has been successfully connected.",
                $kingsChatData['access_token']
            );

            // Clear the pending connection
            session()->forget('kingschat_pending_connection');

            return redirect()->intended(route('dashboard', absolute: false))
                ->with('success', 'Login successful and KingsChat connected!');
        }

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
