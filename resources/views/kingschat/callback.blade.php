<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>KingsChat Authentication</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Connecting KingsChat...</h2>
        <p>Please wait while we complete your authentication.</p>
    </div>

    <script>
        // KingsChat returns tokens directly in URL hash or as POST data
        function parseTokensFromUrl() {
            // Check URL hash first (for token response)
            const hash = window.location.hash.substring(1);
            const hashParams = new URLSearchParams(hash);

            // Check URL search params (for code response)
            const searchParams = new URLSearchParams(window.location.search);

            return {
                accessToken: hashParams.get('access_token') || searchParams.get('accessToken'),
                refreshToken: hashParams.get('refresh_token') || searchParams.get('refreshToken'),
                expiresIn: hashParams.get('expires_in') || searchParams.get('expires_in'),
                error: hashParams.get('error') || searchParams.get('error'),
                userId: hashParams.get('user_id') || searchParams.get('user_id'),
                userName: hashParams.get('user_name') || searchParams.get('user_name')
            };
        }

        // Check if this page was loaded with POST data (from form submission)
        if (document.body.innerHTML.includes('accessToken') || window.location.search.includes('accessToken')) {
            // Handle POST data or URL parameters
            handleAuthenticationData();
        }

        // Listen for POST messages from KingsChat
        window.addEventListener('message', function(event) {
            if (event.origin !== 'https://accounts.kingsch.at') return;

            const data = event.data;
            if (data.accessToken) {
                sendSuccessToParent(data);
            } else if (data.error) {
                sendErrorToParent(data.error);
            }
        });

        function sendSuccessToParent(data) {
            if (window.opener) {
                window.opener.postMessage({
                    type: 'KINGSCHAT_AUTH_SUCCESS',
                    authResult: {
                        accessToken: data.accessToken,
                        refreshToken: data.refreshToken,
                        expiresIn: data.expiresIn || 3600,
                        user: {
                            user_id: data.userId || data.user_id,
                            name: data.userName || data.user_name || 'User'
                        }
                    }
                }, window.location.origin);
            }
            window.close();
        }

        function sendErrorToParent(error) {
            if (window.opener) {
                window.opener.postMessage({
                    type: 'KINGSCHAT_AUTH_ERROR',
                    error: error
                }, window.location.origin);
            }
            window.close();
        }

        function handleAuthenticationData() {
            // This function will be called if POST data is detected
            // The actual POST handling is done by the Laravel controller
            // This is just a fallback for any client-side handling needed
        }

        // Parse tokens from URL
        const tokens = parseTokensFromUrl();

        if (tokens.error) {
            // Send error to parent window
            if (window.opener) {
                window.opener.postMessage({
                    type: 'KINGSCHAT_AUTH_ERROR',
                    error: tokens.error
                }, window.location.origin);
            }
            window.close();
        } else if (tokens.accessToken) {
            // Get user profile with the access token
            fetch('https://connect.kingsch.at/api/profile', {
                headers: {
                    'Authorization': 'Bearer ' + tokens.accessToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(userData => {
                // Send success to parent window
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'KINGSCHAT_AUTH_SUCCESS',
                        authResult: {
                            accessToken: tokens.accessToken,
                            refreshToken: tokens.refreshToken,
                            expiresIn: tokens.expiresIn || 3600,
                            user: {
                                user_id: userData.profile?.user?.user_id || tokens.userId,
                                name: userData.profile?.user?.name || tokens.userName || 'User',
                                email: userData.profile?.email?.address || null
                            }
                        }
                    }, window.location.origin);
                }
                window.close();
            })
            .catch(error => {
                console.error('Error fetching user profile:', error);
                // Still send success with basic user info
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'KINGSCHAT_AUTH_SUCCESS',
                        authResult: {
                            accessToken: tokens.accessToken,
                            refreshToken: tokens.refreshToken,
                            expiresIn: tokens.expiresIn || 3600,
                            user: {
                                user_id: tokens.userId || 'unknown',
                                name: tokens.userName || 'User'
                            }
                        }
                    }, window.location.origin);
                }
                window.close();
            });
        } else {
            // Wait a bit for potential POST message
            setTimeout(() => {
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'KINGSCHAT_AUTH_ERROR',
                        error: 'No authentication data received'
                    }, window.location.origin);
                }
                window.close();
            }, 3000);
        }
    </script>
</body>
</html>
