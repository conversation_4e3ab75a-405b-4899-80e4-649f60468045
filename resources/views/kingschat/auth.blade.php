<!-- KingsChat Authentication Component -->
<div id="kingschat-auth" class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
    <div class="flex items-center mb-3">
        <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <h3 class="text-lg font-semibold text-blue-900">Connect with KingsChat</h3>
    </div>

    <p class="text-sm text-blue-800 mb-4">
        Connect your KingsChat account to receive schedule notifications and stay updated on your appointments.
    </p>

    <div id="kingschat-connect-section">
        <button onclick="loginWithKingsChat()"
                id="kingschat-connect-btn"
                class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
            <span id="kingschat-btn-text">Connect with KingsChat</span>
        </button>

        <p class="text-xs text-blue-700 mt-2 text-center">
            Optional - You can skip this and connect later in your profile settings
        </p>
    </div>

    <div id="kingschat-success-section" class="hidden">
        <div class="flex items-center text-green-800">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="font-medium">KingsChat Connected Successfully!</span>
        </div>
        <p class="text-sm text-green-700 mt-2">
            You'll receive notifications for your scheduled events through KingsChat.
        </p>

        <!-- Debug button to manually trigger connection -->
        <button onclick="window.connectKingsChatAfterAuth()"
                class="mt-3 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600">
            Debug: Connect Now
        </button>

        <!-- Button to check stored data -->
        <button onclick="console.log('Stored data:', sessionStorage.getItem('kingschat_data'))"
                class="mt-3 ml-2 px-3 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600">
            Debug: Check Data
        </button>
    </div>
</div>

<!-- KingsChat Authentication - Simple Direct Redirect -->
<script>
// No SDK needed - using direct redirect like the existing implementation
console.log('KingsChat auth component loaded');
</script>

<script>
// Initialize KingsChat SDK
let kingsChatSDK = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('KingsChat auth component ready');
});

// Function to login with KingsChat - copied from existing implementation
function loginWithKingsChat() {
    const clientId = '619b30ea-a682-47fb-b90f-5b8e780b89ca';
    const scopes = ['conference_calls'];

    // Get the full URL of the callback page
    const baseUrl = window.location.origin;
    const callbackUrl = baseUrl + '/kingschat/callback';

    // Construct the login URL with all necessary parameters
    const params = {
        client_id: clientId,
        scopes: JSON.stringify(scopes),
        redirect_uri: callbackUrl,
        response_type: 'token',
        post_redirect: true
    };

    const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

    const loginUrl = `https://accounts.kingsch.at/?${queryString}`;

    console.log('KingsChat login URL:', loginUrl);
    console.log('Callback URL:', callbackUrl);
    console.log('Base URL:', baseUrl);
    console.log('Current URL:', window.location.href);

    // Add loading state to button
    const button = document.getElementById('kingschat-connect-btn');
    const btnText = document.getElementById('kingschat-btn-text');
    button.disabled = true;
    btnText.innerHTML = '<svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';

    // Redirect after a short delay to show loading state
    setTimeout(() => {
        window.location.href = loginUrl;
    }, 500);
}

// No additional functions needed - the callback handles everything
</script>

<style>
#kingschat-auth {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
