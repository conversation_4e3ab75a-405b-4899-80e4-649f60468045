<?php

namespace App\Http\Controllers;

use App\Services\KingsChatService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class KingsChatController extends Controller
{
    protected KingsChatService $kingsChatService;

    public function __construct(KingsChatService $kingsChatService)
    {
        $this->kingsChatService = $kingsChatService;
    }

    /**
     * Show KingsChat integration settings.
     */
    public function settings(): View
    {
        $user = Auth::user();
        
        return view('kingschat.settings', [
            'user' => $user,
            'isConfigured' => $this->kingsChatService->isConfigured(),
            'hasKingsChatConnected' => $user->hasKingsChatConnected(),
            'notificationsEnabled' => $user->hasKingsChatNotificationsEnabled(),
        ]);
    }

    /**
     * Connect KingsChat account.
     */
    public function connect(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'kingschat_user_id' => 'required|string|max:255',
        ]);

        $user = Auth::user();
        $user->connectKingsChat($validated['kingschat_user_id'], '', '', 3600); // Empty tokens for manual connection

        // Send welcome message
        if ($this->kingsChatService->isConfigured()) {
            $this->kingsChatService->sendWelcomeMessage(
                $validated['kingschat_user_id'],
                $user->name
            );
        }

        return redirect()->route('kingschat.settings')
            ->with('success', 'KingsChat account connected successfully!');
    }

    /**
     * Connect KingsChat account after authentication with tokens.
     */
    public function connectAfterAuth(Request $request)
    {
        Log::info('KingsChat connectAfterAuth called', [
            'request_data' => $request->all(),
            'user_id' => Auth::id(),
            'user_authenticated' => Auth::check()
        ]);

        $validated = $request->validate([
            'user_id' => 'required|string|max:255',
            'access_token' => 'required|string',
            'refresh_token' => 'nullable|string',
            'expires_in' => 'integer|min:1',
            'user_data' => 'array'
        ]);

        $user = Auth::user();

        if (!$user) {
            Log::error('KingsChat connectAfterAuth: User not authenticated');
            return response()->json(['success' => false, 'message' => 'User not authenticated'], 401);
        }

        Log::info('Connecting KingsChat for user', [
            'user_id' => $user->id,
            'kingschat_user_id' => $validated['user_id']
        ]);

        // Connect KingsChat with tokens
        $user->connectKingsChat(
            $validated['user_id'],
            $validated['access_token'],
            $validated['refresh_token'] ?? '',
            $validated['expires_in'] ?? 3600
        );

        // Send welcome message using the user's own access token
        $welcomeSent = $this->kingsChatService->sendMessageWithToken(
            $validated['user_id'],
            "🎉 Welcome to GPDScheduler, {$user->name}! Your KingsChat account has been successfully connected. You'll now receive notifications for your scheduled events.",
            $validated['access_token']
        );

        Log::info('KingsChat connection completed', [
            'user_id' => $user->id,
            'welcome_sent' => $welcomeSent
        ]);

        return response()->json([
            'success' => true,
            'message' => 'KingsChat account connected successfully!',
            'welcome_sent' => $welcomeSent
        ]);
    }

    /**
     * Disconnect KingsChat account.
     */
    public function disconnect(): RedirectResponse
    {
        $user = Auth::user();
        $user->disconnectKingsChat();

        return redirect()->route('kingschat.settings')
            ->with('success', 'KingsChat account disconnected successfully!');
    }

    /**
     * Toggle KingsChat notifications.
     */
    public function toggleNotifications(Request $request): RedirectResponse
    {
        $user = Auth::user();
        
        if (!$user->hasKingsChatConnected()) {
            return redirect()->route('kingschat.settings')
                ->with('error', 'Please connect your KingsChat account first.');
        }

        $enabled = $request->boolean('enabled');
        $user->update(['kingschat_notifications_enabled' => $enabled]);

        $message = $enabled ? 'KingsChat notifications enabled!' : 'KingsChat notifications disabled!';
        
        return redirect()->route('kingschat.settings')
            ->with('success', $message);
    }

    /**
     * Send a test message.
     */
    public function sendTestMessage(): RedirectResponse
    {
        $user = Auth::user();
        
        if (!$user->hasKingsChatConnected()) {
            return redirect()->route('kingschat.settings')
                ->with('error', 'Please connect your KingsChat account first.');
        }

        if (!$this->kingsChatService->isConfigured()) {
            return redirect()->route('kingschat.settings')
                ->with('error', 'KingsChat service is not properly configured.');
        }

        $success = $this->kingsChatService->testConnection($user->kingschat_user_id);
        
        if ($success) {
            return redirect()->route('kingschat.settings')
                ->with('success', 'Test message sent successfully! Check your KingsChat.');
        } else {
            return redirect()->route('kingschat.settings')
                ->with('error', 'Failed to send test message. Please check your connection.');
        }
    }

    /**
     * Show KingsChat OAuth login page.
     */
    public function login(): View
    {
        return view('kingschat.login', [
            'clientId' => config('kingschat.client_id'),
            'scopes' => config('kingschat.scopes'),
        ]);
    }

    /**
     * Handle KingsChat OAuth callback - copied from existing implementation.
     */
    public function callback(Request $request)
    {
        Log::info('KingsChat callback accessed', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'all_data' => $request->all(),
            'headers' => $request->headers->all(),
            'content' => $request->getContent()
        ]);

        // Try to get token from various sources (like existing implementation)
        $token = null;
        $refreshToken = null;

        // Check POST data
        if ($request->has('accessToken')) {
            $token = $request->input('accessToken');
            Log::info('Found accessToken in POST data');
        }

        // Check GET data
        if (!$token && $request->query('accessToken')) {
            $token = $request->query('accessToken');
            Log::info('Found accessToken in GET data');
        }

        // Check JSON input
        $input_data = $request->getContent();
        if (!$token && !empty($input_data)) {
            $json_data = json_decode($input_data, true);
            if ($json_data && isset($json_data['accessToken'])) {
                $token = $json_data['accessToken'];
                if (isset($json_data['refreshToken'])) {
                    $refreshToken = $json_data['refreshToken'];
                }
            }
        }

        // Check for refresh token in various places
        if (!$refreshToken) {
            if ($request->has('refreshToken')) {
                $refreshToken = $request->input('refreshToken');
            } elseif ($request->query('refreshToken')) {
                $refreshToken = $request->query('refreshToken');
            }
        }

        if ($token) {
            Log::info("KingsChat callback received token", [
                'token_preview' => substr($token, 0, 20) . '...',
                'has_refresh_token' => !empty($refreshToken)
            ]);

            // Decode the token to get user information
            $tokenParts = explode('.', $token);
            $tokenUserId = null;
            if (count($tokenParts) === 3) {
                $payload = json_decode(base64_decode(str_replace('_', '/', str_replace('-', '+', $tokenParts[1]))), true);
                if (isset($payload['sub'])) {
                    $tokenUserId = $payload['sub'];
                    Log::info("Token user ID from JWT: " . $tokenUserId);
                }
            }

            // Fetch user profile from KingsChat API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get('https://connect.kingsch.at/api/profile');

            if ($response->successful()) {
                $userData = $response->json();
                Log::info("KingsChat user profile fetched successfully");

                // Extract user ID and name from the profile data
                $userId = isset($userData['profile']['user']['user_id']) ? $userData['profile']['user']['user_id'] : $tokenUserId;
                $userName = isset($userData['profile']['user']['name']) ? $userData['profile']['user']['name'] : 'User';

                // Store in session for potential connection after Laravel auth
                session([
                    'kingschat_pending_connection' => [
                        'user_id' => $userId,
                        'access_token' => $token,
                        'refresh_token' => $refreshToken,
                        'expires_in' => 3600,
                        'user_data' => $userData
                    ]
                ]);

                // If user is already authenticated, connect immediately
                if (Auth::check()) {
                    $user = Auth::user();
                    $user->connectKingsChat($userId, $token, $refreshToken ?: '', 3600);

                    // Send welcome message
                    $this->kingsChatService->sendMessageWithToken(
                        $userId,
                        "🎉 Welcome to GPDScheduler, {$user->name}! Your KingsChat account has been successfully connected.",
                        $token
                    );

                    return redirect()->route('dashboard')->with('success', 'KingsChat connected successfully!');
                } else {
                    // Redirect to login with success message
                    return redirect()->route('login')->with('success', 'KingsChat authentication successful! Please login to complete the connection.');
                }
            } else {
                Log::error("Failed to fetch KingsChat user profile", [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return redirect()->route('login')->with('error', 'Failed to fetch user profile from KingsChat.');
            }
        }

        Log::error("KingsChat callback: No authentication data received");
        return redirect()->route('login')->with('error', 'No authentication data received from KingsChat.');
    }

    /**
     * Handle KingsChat OAuth callback POST data.
     */
    public function callbackPost(Request $request)
    {
        // Delegate to the main callback method
        return $this->callback($request);
    }

    /**
     * Test page for KingsChat authentication debugging.
     */
    public function test(Request $request): View
    {
        return view('kingschat.test', [
            'user' => Auth::user(),
            'isAuthenticated' => Auth::check()
        ]);
    }

    /**
     * Exchange authorization code for access token.
     */
    public function exchangeCode(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string',
            'state' => 'nullable|string'
        ]);

        try {
            // Exchange code for tokens using KingsChat OAuth endpoint
            $response = Http::post('https://connect.kingsch.at/oauth2/token', [
                'client_id' => config('kingschat.client_id'),
                'client_secret' => config('kingschat.client_secret'),
                'code' => $validated['code'],
                'grant_type' => 'authorization_code',
                'redirect_uri' => route('kingschat.callback')
            ]);

            if ($response->successful()) {
                $tokenData = $response->json();

                // Get user info using the access token
                $userResponse = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $tokenData['access_token']
                ])->get('https://connect.kingsch.at/api/user');

                if ($userResponse->successful()) {
                    $userData = $userResponse->json();

                    return response()->json([
                        'success' => true,
                        'access_token' => $tokenData['access_token'],
                        'refresh_token' => $tokenData['refresh_token'],
                        'expires_in' => $tokenData['expires_in'] ?? 3600,
                        'user' => [
                            'user_id' => $userData['user_id'] ?? $userData['id'],
                            'name' => $userData['name'] ?? $userData['display_name'],
                            'email' => $userData['email'] ?? null
                        ]
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to get user information'
                    ], 400);
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to exchange authorization code'
                ], 400);
            }
        } catch (Exception $e) {
            Log::error('KingsChat code exchange error:', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Authentication failed'
            ], 500);
        }
    }

    /**
     * Show all users with KingsChat notifications enabled.
     */
    public function notificationUsers(): View
    {
        $this->authorize('viewAny', \App\Models\User::class); // Only admins can view this
        
        $users = \App\Models\User::whereNotNull('kingschat_user_id')
            ->where('kingschat_notifications_enabled', true)
            ->with(['schedules' => function ($query) {
                $query->where('kingschat_notification', true)
                      ->where('start_time', '>', now())
                      ->orderBy('start_time');
            }])
            ->paginate(20);

        return view('kingschat.notification-users', compact('users'));
    }

    /**
     * Send bulk notification to all users with KingsChat enabled.
     */
    public function sendBulkNotification(Request $request): RedirectResponse
    {
        $this->authorize('create', \App\Models\User::class); // Only admins can send bulk notifications
        
        $validated = $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        $users = \App\Models\User::whereNotNull('kingschat_user_id')
            ->where('kingschat_notifications_enabled', true)
            ->get();

        $successCount = 0;
        $failureCount = 0;

        foreach ($users as $user) {
            $success = $this->kingsChatService->sendMessage(
                $user->kingschat_user_id,
                $validated['message']
            );
            
            if ($success) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        $message = "Bulk notification sent! Success: {$successCount}, Failed: {$failureCount}";
        
        return redirect()->route('kingschat.notification-users')
            ->with('success', $message);
    }
}
