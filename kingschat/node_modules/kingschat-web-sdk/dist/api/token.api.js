"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.refreshAuthenticationTokenRequest = void 0;

var _constants = require("../constants");

const refreshAuthenticationTokenRequest = ({
  refreshAuthenticationTokenOptions,
  environment = 'prod'
}) => {
  return fetch(`${_constants.kingsChatApiPaths[environment]}/oauth2/token`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      client_id: refreshAuthenticationTokenOptions.clientId,
      grant_type: 'refresh_token',
      refresh_token: refreshAuthenticationTokenOptions.refreshToken
    })
  }).then(response => {
    if (response.ok) {
      return response.json().then(payload => {
        return {
          accessToken: payload.access_token,
          expiresInMillis: payload.expires_in_millis,
          refreshToken: payload.refresh_token
        };
      });
    }

    return Promise.reject(Error('error'));
  }).catch(error => {
    return Promise.reject(Error(error.message));
  });
};

exports.refreshAuthenticationTokenRequest = refreshAuthenticationTokenRequest;
var _default = {
  refreshAuthenticationTokenRequest
};
exports.default = _default;