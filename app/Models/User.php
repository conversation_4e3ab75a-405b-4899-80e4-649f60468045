<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'kingschat_user_id',
        'kingschat_access_token',
        'kingschat_refresh_token',
        'kingschat_token_expires_at',
        'kingschat_notifications_enabled',
        'kingschat_connected_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'kingschat_notifications_enabled' => 'boolean',
            'kingschat_connected_at' => 'datetime',
            'kingschat_token_expires_at' => 'datetime',
        ];
    }

    /**
     * Check if user has admin role.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user has manager role.
     */
    public function isManager(): bool
    {
        return $this->role === 'manager';
    }

    /**
     * Check if user has specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Get the schedules for the user.
     */
    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    /**
     * Get active schedules for the user.
     */
    public function activeSchedules(): HasMany
    {
        return $this->schedules()->where('status', 'active');
    }

    /**
     * Get upcoming schedules for the user.
     */
    public function upcomingSchedules(): HasMany
    {
        return $this->schedules()->where('start_time', '>', now());
    }

    /**
     * Check if user has KingsChat connected.
     */
    public function hasKingsChatConnected(): bool
    {
        return !empty($this->kingschat_user_id);
    }

    /**
     * Check if user has KingsChat notifications enabled.
     */
    public function hasKingsChatNotificationsEnabled(): bool
    {
        return $this->kingschat_notifications_enabled && $this->hasKingsChatConnected();
    }

    /**
     * Connect KingsChat account to user with tokens.
     */
    public function connectKingsChat(string $kingschatUserId, string $accessToken, string $refreshToken, int $expiresIn = 3600): void
    {
        $this->update([
            'kingschat_user_id' => $kingschatUserId,
            'kingschat_access_token' => $accessToken,
            'kingschat_refresh_token' => $refreshToken,
            'kingschat_token_expires_at' => now()->addSeconds($expiresIn),
            'kingschat_notifications_enabled' => true,
            'kingschat_connected_at' => now(),
        ]);
    }

    /**
     * Disconnect KingsChat account from user.
     */
    public function disconnectKingsChat(): void
    {
        $this->update([
            'kingschat_user_id' => null,
            'kingschat_access_token' => null,
            'kingschat_refresh_token' => null,
            'kingschat_token_expires_at' => null,
            'kingschat_notifications_enabled' => false,
            'kingschat_connected_at' => null,
        ]);
    }
}
