"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.kingsChatApiPaths = exports.allowedResponseOrigins = exports.authorizationURLs = void 0;
const authorizationURLs = {
  dev: 'http://localhost:5050',
  // Development
  staging: 'https://accounts.staging.kingsch.at',
  // Staging ENV
  prod: 'https://accounts.kingsch.at' // Production ENV

};
exports.authorizationURLs = authorizationURLs;
const allowedResponseOrigins = ['http://localhost:5050', // Development
'https://accounts.staging.kingsch.at', // Testing
'https://accounts.kingsch.at'];
exports.allowedResponseOrigins = allowedResponseOrigins;
const kingsChatApiPaths = {
  dev: 'http://localhost:8000',
  // Development
  staging: 'https://kc-connect.appunite.com',
  // Staging ENV
  prod: 'https://connect.kingsch.at' // Production ENV

};
exports.kingsChatApiPaths = kingsChatApiPaths;
var _default = {
  authorizationURLs,
  allowedResponseOrigins,
  kingsChatApiPaths
};
exports.default = _default;