<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('kingschat_user_id')->nullable()->after('role');
            $table->text('kingschat_access_token')->nullable()->after('kingschat_user_id');
            $table->text('kingschat_refresh_token')->nullable()->after('kingschat_access_token');
            $table->timestamp('kingschat_token_expires_at')->nullable()->after('kingschat_refresh_token');
            $table->boolean('kingschat_notifications_enabled')->default(false)->after('kingschat_token_expires_at');
            $table->timestamp('kingschat_connected_at')->nullable()->after('kingschat_notifications_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'kingschat_user_id',
                'kingschat_access_token',
                'kingschat_refresh_token',
                'kingschat_token_expires_at',
                'kingschat_notifications_enabled',
                'kingschat_connected_at'
            ]);
        });
    }
};
