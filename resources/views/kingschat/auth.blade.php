<!-- KingsChat Authentication Component -->
<div id="kingschat-auth" class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
    <div class="flex items-center mb-3">
        <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <h3 class="text-lg font-semibold text-blue-900">Connect with KingsChat</h3>
    </div>

    <p class="text-sm text-blue-800 mb-4">
        Connect your KingsChat account to receive schedule notifications and stay updated on your appointments.
    </p>

    <div id="kingschat-connect-section">
        <button onclick="loginWithKingsChat()"
                id="kingschat-connect-btn"
                class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
            <span id="kingschat-btn-text">Connect with KingsChat</span>
        </button>

        <p class="text-xs text-blue-700 mt-2 text-center">
            Optional - You can skip this and connect later in your profile settings
        </p>
    </div>

    <div id="kingschat-success-section" class="hidden">
        <div class="flex items-center text-green-800">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="font-medium">KingsChat Connected Successfully!</span>
        </div>
        <p class="text-sm text-green-700 mt-2">
            You'll receive notifications for your scheduled events through KingsChat.
        </p>
    </div>
</div>

<!-- KingsChat Web SDK -->
<script src="{{ asset('kingschat/node_modules/kingschat-web-sdk/dist/kingschat-web-sdk.min.js') }}"></script>

<script>
// Initialize KingsChat SDK
let kingsChatSDK = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize KingsChat SDK
    try {
        kingsChatSDK = new KingsChatWebSDK({
            clientId: '{{ config("kingschat.client_id", "com.kingschat") }}',
            scopes: ['user', 'profile', 'send_chat_message']
        });
        console.log('KingsChat SDK initialized successfully');
    } catch (error) {
        console.error('Failed to initialize KingsChat SDK:', error);
    }
});

// Function to login with KingsChat
async function loginWithKingsChat() {
    const connectBtn = document.getElementById('kingschat-connect-btn');
    const btnText = document.getElementById('kingschat-btn-text');
    const connectSection = document.getElementById('kingschat-connect-section');
    const successSection = document.getElementById('kingschat-success-section');

    if (!kingsChatSDK) {
        alert('KingsChat SDK not initialized. Please try again.');
        return;
    }

    try {
        // Update button state
        connectBtn.disabled = true;
        btnText.textContent = 'Connecting...';

        // Authenticate with KingsChat
        const authResult = await kingsChatSDK.authenticate();

        if (authResult && authResult.accessToken) {
            console.log('KingsChat authentication successful:', authResult);

            // Store the authentication data
            const kingsChatData = {
                user_id: authResult.user.user_id,
                access_token: authResult.accessToken,
                refresh_token: authResult.refreshToken,
                expires_in: authResult.expiresIn || 3600,
                user_data: authResult.user
            };

            // Store in sessionStorage to persist during registration/login
            sessionStorage.setItem('kingschat_data', JSON.stringify(kingsChatData));

            // Show success state
            connectSection.classList.add('hidden');
            successSection.classList.remove('hidden');

            console.log('KingsChat data stored for post-auth connection');

        } else {
            throw new Error('Authentication failed - no access token received');
        }

    } catch (error) {
        console.error('KingsChat authentication error:', error);
        alert('Failed to connect with KingsChat. Please try again.');

        // Reset button state
        connectBtn.disabled = false;
        btnText.textContent = 'Connect with KingsChat';
    }
}

// Function to send KingsChat data after successful Laravel auth
window.connectKingsChatAfterAuth = function() {
    const data = sessionStorage.getItem('kingschat_data');
    if (data) {
        const kingsChatData = JSON.parse(data);

        // Send AJAX request to connect KingsChat
        fetch('/kingschat/connect-after-auth', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(kingsChatData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('KingsChat connected successfully to Laravel user');
                // Clear stored data
                sessionStorage.removeItem('kingschat_data');

                // Show success message if on dashboard
                if (window.location.pathname === '/dashboard') {
                    showNotification('KingsChat connected successfully!', 'success');
                }
            } else {
                console.error('Failed to connect KingsChat to Laravel user:', data.message);
            }
        })
        .catch(error => {
            console.error('Error connecting KingsChat to Laravel user:', error);
        });
    }
};

// Auto-connect KingsChat if user just logged in and has stored data
if (window.location.pathname === '/dashboard' && sessionStorage.getItem('kingschat_data')) {
    // Wait a bit for the page to fully load
    setTimeout(() => {
        window.connectKingsChatAfterAuth();
    }, 1000);
}

// Helper function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}
</script>

<style>
#kingschat-auth {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
