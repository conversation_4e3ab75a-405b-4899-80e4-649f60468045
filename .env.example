APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# KingsChat Configuration
KINGSCHAT_API_URL=https://connect.kingsch.at/api
KINGSCHAT_SYSTEM_USER_ID=67c6d4860b20977035865f98
KINGSCHAT_SYSTEM_ACCESS_TOKEN=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
KINGSCHAT_CLIENT_ID=619b30ea-a682-47fb-b90f-5b8e780b89ca
KINGSCHAT_CLIENT_SECRET=your_client_secret_here

# KingsChat Notification Settings
KINGSCHAT_WELCOME_ENABLED=true
KINGSCHAT_SCHEDULE_REMINDERS_ENABLED=true
KINGSCHAT_SCHEDULE_CREATED_ENABLED=true
KINGSCHAT_SCHEDULE_UPDATED_ENABLED=true
KINGSCHAT_SCHEDULE_COMPLETED_ENABLED=true
KINGSCHAT_REMINDER_ADVANCE_MINUTES=15

# KingsChat Rate Limiting
KINGSCHAT_RATE_LIMIT_PER_MINUTE=60
KINGSCHAT_RATE_LIMIT_PER_HOUR=1000

# KingsChat Logging
KINGSCHAT_LOGGING_ENABLED=true
KINGSCHAT_LOG_SUCCESS=true
KINGSCHAT_LOG_FAILURES=true

# KingsChat Testing
KINGSCHAT_TEST_USER_ID=your_test_user_id_here
KINGSCHAT_MOCK_RESPONSES=false

# KingsChat Configuration
KINGSCHAT_API_URL=https://connect.kingsch.at/api
KINGSCHAT_SYSTEM_USER_ID=67c6d4860b20977035865f98
KINGSCHAT_ACCESS_TOKEN=your_access_token_here
KINGSCHAT_CLIENT_ID=your_client_id_here
KINGSCHAT_CLIENT_SECRET=your_client_secret_here

# KingsChat Notification Settings
KINGSCHAT_WELCOME_ENABLED=true
KINGSCHAT_SCHEDULE_REMINDERS_ENABLED=true
KINGSCHAT_SCHEDULE_CREATED_ENABLED=true
KINGSCHAT_SCHEDULE_UPDATED_ENABLED=true
KINGSCHAT_SCHEDULE_COMPLETED_ENABLED=true
KINGSCHAT_REMINDER_ADVANCE_MINUTES=15

# KingsChat Rate Limiting
KINGSCHAT_RATE_LIMIT_PER_MINUTE=60
KINGSCHAT_RATE_LIMIT_PER_HOUR=1000

# KingsChat Logging
KINGSCHAT_LOGGING_ENABLED=true
KINGSCHAT_LOG_SUCCESS=true
KINGSCHAT_LOG_FAILURES=true

# KingsChat Testing
KINGSCHAT_TEST_USER_ID=your_test_user_id_here
KINGSCHAT_MOCK_RESPONSES=false
