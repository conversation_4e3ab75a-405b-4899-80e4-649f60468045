<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\KingsChatController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ScheduleController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Schedule routes
    Route::resource('schedules', ScheduleController::class);
    Route::patch('/schedules/{schedule}/complete', [ScheduleController::class, 'complete'])->name('schedules.complete');
    Route::get('/kingschat-notifications', [ScheduleController::class, 'kingsChatNotifications'])->name('schedules.kingschat');

    // KingsChat routes
    Route::prefix('kingschat')->name('kingschat.')->group(function () {
        Route::get('/settings', [KingsChatController::class, 'settings'])->name('settings');
        Route::post('/connect', [KingsChatController::class, 'connect'])->name('connect');
        Route::post('/connect-after-auth', [KingsChatController::class, 'connectAfterAuth'])->name('connect-after-auth');
        Route::delete('/disconnect', [KingsChatController::class, 'disconnect'])->name('disconnect');
        Route::post('/toggle-notifications', [KingsChatController::class, 'toggleNotifications'])->name('toggle-notifications');
        Route::post('/test', [KingsChatController::class, 'sendTestMessage'])->name('test');
        Route::get('/login', [KingsChatController::class, 'login'])->name('login');
        Route::get('/callback', [KingsChatController::class, 'callback'])->name('callback');
        Route::post('/exchange-code', [KingsChatController::class, 'exchangeCode'])->name('exchange-code');

        // Admin routes
        Route::middleware('can:viewAny,App\Models\User')->group(function () {
            Route::get('/notification-users', [KingsChatController::class, 'notificationUsers'])->name('notification-users');
            Route::post('/bulk-notification', [KingsChatController::class, 'sendBulkNotification'])->name('bulk-notification');
        });
    });
});

require __DIR__.'/auth.php';
