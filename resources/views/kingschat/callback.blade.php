<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KingsChat Authentication</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Connecting KingsChat...</h2>
        <p>Please wait while we complete your authentication.</p>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');
        const state = urlParams.get('state');

        if (error) {
            // Send error to parent window
            if (window.opener) {
                window.opener.postMessage({
                    type: 'KINGSCHAT_AUTH_ERROR',
                    error: error
                }, window.location.origin);
            }
            window.close();
        } else if (code) {
            // Exchange code for tokens
            fetch('/kingschat/exchange-code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    code: code,
                    state: state
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Send success to parent window
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'KINGSCHAT_AUTH_SUCCESS',
                            authResult: {
                                accessToken: data.access_token,
                                refreshToken: data.refresh_token,
                                expiresIn: data.expires_in,
                                user: data.user
                            }
                        }, window.location.origin);
                    }
                } else {
                    // Send error to parent window
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'KINGSCHAT_AUTH_ERROR',
                            error: data.message || 'Authentication failed'
                        }, window.location.origin);
                    }
                }
                window.close();
            })
            .catch(error => {
                console.error('Error exchanging code:', error);
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'KINGSCHAT_AUTH_ERROR',
                        error: 'Failed to exchange authorization code'
                    }, window.location.origin);
                }
                window.close();
            });
        } else {
            // No code or error, something went wrong
            if (window.opener) {
                window.opener.postMessage({
                    type: 'KINGSCHAT_AUTH_ERROR',
                    error: 'No authorization code received'
                }, window.location.origin);
            }
            window.close();
        }
    </script>
</body>
</html>
