<?php

return [
    /*
    |--------------------------------------------------------------------------
    | KingsChat API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for KingsChat API integration.
    | You can obtain these values from the KingsChat Developer Portal.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | API Base URL
    |--------------------------------------------------------------------------
    |
    | The base URL for KingsChat API endpoints.
    |
    */
    'api_url' => env('KINGSCHAT_API_URL', 'https://connect.kingsch.at/api'),

    /*
    |--------------------------------------------------------------------------
    | System User ID
    |--------------------------------------------------------------------------
    |
    | The KingsChat user ID of the system account that will send notifications.
    | This should be a dedicated system account for your application.
    |
    */
    'system_user_id' => env('KINGSCHAT_SYSTEM_USER_ID', '67c6d4860b20977035865f98'),

    /*
    |--------------------------------------------------------------------------
    | System Access Token
    |--------------------------------------------------------------------------
    |
    | The access token for the system account. This token should have the
    | 'send_chat_message' scope to send messages to users.
    |
    | IMPORTANT: Keep this token secure and never expose it in client-side code.
    |
    */
    'system_access_token' => env('KINGSCHAT_SYSTEM_ACCESS_TOKEN'),

    /*
    |--------------------------------------------------------------------------
    | Client ID
    |--------------------------------------------------------------------------
    |
    | Your application's client ID from the KingsChat Developer Portal.
    | This is used for OAuth authentication flows.
    |
    */
    'client_id' => env('KINGSCHAT_CLIENT_ID', '619b30ea-a682-47fb-b90f-5b8e780b89ca'),

    /*
    |--------------------------------------------------------------------------
    | Client Secret
    |--------------------------------------------------------------------------
    |
    | Your application's client secret from the KingsChat Developer Portal.
    | Keep this secure and never expose it in client-side code.
    |
    */
    'client_secret' => env('KINGSCHAT_CLIENT_SECRET'),

    /*
    |--------------------------------------------------------------------------
    | OAuth Scopes
    |--------------------------------------------------------------------------
    |
    | The scopes your application requests when users authenticate.
    | Common scopes include: user, profile, send_chat_message
    |
    */
    'scopes' => [
        'user',
        'profile',
        'send_chat_message',
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for different types of notifications.
    |
    */
    'notifications' => [
        'welcome_message' => [
            'enabled' => env('KINGSCHAT_WELCOME_ENABLED', true),
            'template' => 'Welcome to GPDScheduler, {name}! Your login was successful. You can now manage your schedules and receive notifications through KingsChat.',
        ],
        
        'schedule_reminder' => [
            'enabled' => env('KINGSCHAT_SCHEDULE_REMINDERS_ENABLED', true),
            'advance_minutes' => env('KINGSCHAT_REMINDER_ADVANCE_MINUTES', 15), // Send reminder 15 minutes before
        ],
        
        'schedule_created' => [
            'enabled' => env('KINGSCHAT_SCHEDULE_CREATED_ENABLED', true),
        ],
        
        'schedule_updated' => [
            'enabled' => env('KINGSCHAT_SCHEDULE_UPDATED_ENABLED', true),
        ],
        
        'schedule_completed' => [
            'enabled' => env('KINGSCHAT_SCHEDULE_COMPLETED_ENABLED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Rate limiting settings to prevent API abuse.
    |
    */
    'rate_limit' => [
        'max_messages_per_minute' => env('KINGSCHAT_RATE_LIMIT_PER_MINUTE', 60),
        'max_messages_per_hour' => env('KINGSCHAT_RATE_LIMIT_PER_HOUR', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Enable or disable logging of KingsChat API interactions.
    |
    */
    'logging' => [
        'enabled' => env('KINGSCHAT_LOGGING_ENABLED', true),
        'log_successful_sends' => env('KINGSCHAT_LOG_SUCCESS', true),
        'log_failed_sends' => env('KINGSCHAT_LOG_FAILURES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Testing
    |--------------------------------------------------------------------------
    |
    | Configuration for testing KingsChat integration.
    |
    */
    'testing' => [
        'test_user_id' => env('KINGSCHAT_TEST_USER_ID'),
        'mock_responses' => env('KINGSCHAT_MOCK_RESPONSES', false),
    ],
];
