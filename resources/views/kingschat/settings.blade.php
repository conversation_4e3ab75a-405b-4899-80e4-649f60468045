<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('KingsChat Integration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Connection Status Card -->
            <div class="bg-white overflow-hidden shadow-lg rounded-xl">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Connection Status</h3>
                </div>
                <div class="p-6">
                    @if($hasKingsChatConnected)
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-green-800 font-medium">Connected to KingsChat</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">
                            Your KingsChat account is connected. You can receive notifications and messages through KingsChat.
                        </p>
                        <div class="flex space-x-3">
                            <form method="POST" action="{{ route('kingschat.test') }}">
                                @csrf
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                                    Send Test Message
                                </button>
                            </form>
                            <form method="POST" action="{{ route('kingschat.disconnect') }}" onsubmit="return confirm('Are you sure you want to disconnect KingsChat?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                                    Disconnect
                                </button>
                            </form>
                        </div>
                    @else
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                            <span class="text-gray-600 font-medium">Not Connected</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-6">
                            Connect your KingsChat account to receive schedule notifications and messages.
                        </p>
                        
                        <!-- Manual Connection Form -->
                        <form method="POST" action="{{ route('kingschat.connect') }}" class="space-y-4">
                            @csrf
                            <div>
                                <x-input-label for="kingschat_user_id" :value="__('KingsChat User ID')" class="text-sm font-semibold text-gray-700" />
                                <x-text-input id="kingschat_user_id" 
                                    class="block w-full mt-2 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200" 
                                    type="text" 
                                    name="kingschat_user_id" 
                                    :value="old('kingschat_user_id')" 
                                    required 
                                    placeholder="Enter your KingsChat User ID" />
                                <p class="mt-2 text-sm text-gray-500">
                                    You can find your KingsChat User ID in your KingsChat profile settings.
                                </p>
                                <x-input-error :messages="$errors->get('kingschat_user_id')" class="mt-2" />
                            </div>
                            <button type="submit" class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                                Connect KingsChat
                            </button>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Notification Settings Card -->
            @if($hasKingsChatConnected)
                <div class="bg-white overflow-hidden shadow-lg rounded-xl">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Notification Settings</h3>
                    </div>
                    <div class="p-6">
                        <form method="POST" action="{{ route('kingschat.toggle-notifications') }}">
                            @csrf
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-base font-medium text-gray-900">Schedule Notifications</h4>
                                    <p class="text-sm text-gray-600">Receive notifications for your scheduled events through KingsChat</p>
                                </div>
                                <div class="flex items-center">
                                    <input type="hidden" name="enabled" value="0">
                                    <input id="notifications_enabled" 
                                        type="checkbox" 
                                        name="enabled" 
                                        value="1"
                                        {{ $notificationsEnabled ? 'checked' : '' }}
                                        onchange="this.form.submit()"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500 focus:ring-2 transition-all duration-200">
                                    <label for="notifications_enabled" class="ml-3 text-sm font-medium text-gray-700">
                                        {{ $notificationsEnabled ? 'Enabled' : 'Disabled' }}
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            @endif

            <!-- Information Card -->
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <div class="flex items-start">
                    <svg class="w-6 h-6 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h4 class="text-base font-medium text-blue-900 mb-2">About KingsChat Integration</h4>
                        <div class="text-sm text-blue-800 space-y-2">
                            <p>• Receive real-time notifications for your scheduled events</p>
                            <p>• Get reminders before important meetings and appointments</p>
                            <p>• Stay updated on schedule changes and completions</p>
                            <p>• Secure integration with your KingsChat account</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Status -->
            @if(!$isConfigured)
                <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="text-base font-medium text-yellow-900 mb-2">Service Configuration Required</h4>
                            <p class="text-sm text-yellow-800">
                                KingsChat integration is not fully configured. Please contact your administrator to complete the setup.
                            </p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
