import { loginOptionsI, authenticationTokenResponseI, refreshAuthenticationTokenOptionsI, sendMessageOptionsI } from './interfaces';
/**
 * This request user permission to use his/her account for any listed scope
 * @returns {authenticationTokenResponseI} authenticationTokenResponse
 * @param {loginOptionsI} loginOptions
 * @param {env} environment
 */
export declare const login: (loginOptions: loginOptionsI, environment?: "dev" | "staging" | "prod" | undefined) => Promise<authenticationTokenResponseI>;
/**
 * This refresh access token received earlier
 * @returns {authenticationTokenResponseI} authenticationTokenResponse
 * @param {refreshAuthenticationTokenOptionsI} refreshAuthenticationTokenOptions
 * @param {env} environment
 */
export declare const refreshAuthenticationToken: (refreshAuthenticationTokenOptions: refreshAuthenticationTokenOptionsI, environment?: "dev" | "staging" | "prod" | undefined) => Promise<authenticationTokenResponseI>;
/**
 * This request send message to another KingsChat user
 * @returns {string} info
 * @param {sendMessageOptionsI} sendMessageOptions
 * @param {env} environment - optional environment change
 */
export declare const sendMessage: (sendMessageOptions: sendMessageOptionsI, environment?: "dev" | "staging" | "prod" | undefined) => Promise<string>;
declare const _default: {
    login: (loginOptions: loginOptionsI, environment?: "dev" | "staging" | "prod" | undefined) => Promise<authenticationTokenResponseI>;
    refreshAuthenticationToken: (refreshAuthenticationTokenOptions: refreshAuthenticationTokenOptionsI, environment?: "dev" | "staging" | "prod" | undefined) => Promise<authenticationTokenResponseI>;
    sendMessage: (sendMessageOptions: sendMessageOptionsI, environment?: "dev" | "staging" | "prod" | undefined) => Promise<string>;
};
export default _default;
