<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class KingsChatService
{
    private string $baseUrl = 'https://connect.kingsch.at/api';
    private string $oauthUrl = 'https://connect.kingsch.at/oauth2';
    private string $systemUserId;
    private ?string $systemAccessToken;

    public function __construct()
    {
        // System account details from the documentation
        $this->systemUserId = config('kingschat.system_user_id', '67c6d4860b20977035865f98');
        $this->systemAccessToken = config('kingschat.system_access_token');
    }

    /**
     * Send a message to a KingsChat user using system token
     *
     * @param string $recipientId The KingsChat user ID of the recipient
     * @param string $message The message content to send
     * @return bool True if message was sent successfully, false otherwise
     */
    public function sendMessage(string $recipientId, string $message): bool
    {
        if (!$this->systemAccessToken) {
            Log::error("System access token not configured for KingsChat");
            return false;
        }

        return $this->sendMessageWithToken($recipientId, $message, $this->systemAccessToken);
    }

    /**
     * Send a message to a KingsChat user using a specific access token
     *
     * @param string $recipientId The KingsChat user ID of the recipient
     * @param string $message The message content to send
     * @param string $accessToken The access token to use
     * @return bool True if message was sent successfully, false otherwise
     */
    public function sendMessageWithToken(string $recipientId, string $message, string $accessToken): bool
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/users/{$recipientId}/new_message", [
                'message' => [
                    'body' => [
                        'text' => [
                            'body' => $message
                        ]
                    ]
                ]
            ]);

            if ($response->successful()) {
                Log::info("KingsChat message sent successfully to user: {$recipientId}");
                return true;
            } else {
                Log::error("Failed to send KingsChat message", [
                    'recipient_id' => $recipientId,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (Exception $e) {
            Log::error("Exception while sending KingsChat message", [
                'recipient_id' => $recipientId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send a welcome message to a user after login
     *
     * @param string $recipientId The KingsChat user ID
     * @param string $userName The user's name for personalization
     * @return bool True if message was sent successfully
     */
    public function sendWelcomeMessage(string $recipientId, string $userName = 'User'): bool
    {
        $message = "Welcome to GPDScheduler, {$userName}! Your login was successful. You can now manage your schedules and receive notifications through KingsChat.";
        
        return $this->sendMessage($recipientId, $message);
    }

    /**
     * Send a schedule notification to a user
     *
     * @param string $recipientId The KingsChat user ID
     * @param array $scheduleData Schedule information
     * @return bool True if notification was sent successfully
     */
    public function sendScheduleNotification(string $recipientId, array $scheduleData): bool
    {
        $title = $scheduleData['title'] ?? 'Schedule Reminder';
        $startTime = $scheduleData['start_time'] ?? '';
        $location = $scheduleData['location'] ?? '';
        
        $message = "📅 Schedule Reminder: {$title}\n";
        $message .= "⏰ Time: {$startTime}\n";
        
        if ($location) {
            $message .= "📍 Location: {$location}\n";
        }
        
        $message .= "\nDon't forget about your upcoming schedule!";
        
        return $this->sendMessage($recipientId, $message);
    }

    /**
     * Send a schedule creation confirmation
     *
     * @param string $recipientId The KingsChat user ID
     * @param array $scheduleData Schedule information
     * @return bool True if confirmation was sent successfully
     */
    public function sendScheduleCreatedNotification(string $recipientId, array $scheduleData): bool
    {
        $title = $scheduleData['title'] ?? 'New Schedule';
        $startTime = $scheduleData['start_time'] ?? '';
        
        $message = "✅ Schedule Created Successfully!\n\n";
        $message .= "📋 Title: {$title}\n";
        $message .= "⏰ Start Time: {$startTime}\n";
        $message .= "\nYour schedule has been added to GPDScheduler.";
        
        return $this->sendMessage($recipientId, $message);
    }

    /**
     * Send a schedule update notification
     *
     * @param string $recipientId The KingsChat user ID
     * @param array $scheduleData Schedule information
     * @return bool True if notification was sent successfully
     */
    public function sendScheduleUpdatedNotification(string $recipientId, array $scheduleData): bool
    {
        $title = $scheduleData['title'] ?? 'Schedule';
        $startTime = $scheduleData['start_time'] ?? '';
        
        $message = "🔄 Schedule Updated!\n\n";
        $message .= "📋 Title: {$title}\n";
        $message .= "⏰ New Time: {$startTime}\n";
        $message .= "\nYour schedule has been updated in GPDScheduler.";
        
        return $this->sendMessage($recipientId, $message);
    }

    /**
     * Send a schedule completion notification
     *
     * @param string $recipientId The KingsChat user ID
     * @param array $scheduleData Schedule information
     * @return bool True if notification was sent successfully
     */
    public function sendScheduleCompletedNotification(string $recipientId, array $scheduleData): bool
    {
        $title = $scheduleData['title'] ?? 'Schedule';
        
        $message = "🎉 Schedule Completed!\n\n";
        $message .= "📋 {$title}\n";
        $message .= "\nGreat job! You've successfully completed your scheduled task.";
        
        return $this->sendMessage($recipientId, $message);
    }

    /**
     * Test the KingsChat connection
     *
     * @param string $testRecipientId A test user ID to send a test message to
     * @return bool True if test message was sent successfully
     */
    public function testConnection(string $testRecipientId): bool
    {
        $message = "🧪 Test Message from GPDScheduler\n\nThis is a test message to verify KingsChat integration is working correctly.";
        
        return $this->sendMessage($testRecipientId, $message);
    }

    /**
     * Refresh an access token using refresh token
     *
     * @param string $refreshToken The refresh token
     * @param string $clientId The client ID (user ID)
     * @return array|null Array with new tokens or null on failure
     */
    public function refreshAccessToken(string $refreshToken, string $clientId): ?array
    {
        try {
            $response = Http::post("{$this->oauthUrl}/token", [
                'client_id' => $clientId,
                'refresh_token' => $refreshToken,
                'grant_type' => 'refresh_token'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                Log::info("KingsChat access token refreshed successfully for client: {$clientId}");

                return [
                    'access_token' => $data['access_token'] ?? null,
                    'refresh_token' => $data['refresh_token'] ?? $refreshToken, // Keep old if not provided
                    'expires_in' => $data['expires_in'] ?? 3600, // Default 1 hour
                    'expires_at' => now()->addSeconds($data['expires_in'] ?? 3600)
                ];
            } else {
                Log::error("Failed to refresh KingsChat access token", [
                    'client_id' => $clientId,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return null;
            }
        } catch (Exception $e) {
            Log::error("Exception while refreshing KingsChat access token", [
                'client_id' => $clientId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get a valid access token for a user (refresh if needed)
     *
     * @param \App\Models\User $user The user
     * @return string|null Valid access token or null
     */
    public function getValidAccessToken(\App\Models\User $user): ?string
    {
        if (!$user->hasKingsChatConnected()) {
            return null;
        }

        // Check if current token is still valid (assuming 1 hour expiry)
        if ($user->kingschat_token_expires_at && $user->kingschat_token_expires_at->isFuture()) {
            return $user->kingschat_access_token;
        }

        // Token expired, try to refresh
        if ($user->kingschat_refresh_token) {
            $newTokens = $this->refreshAccessToken(
                $user->kingschat_refresh_token,
                $user->kingschat_user_id
            );

            if ($newTokens) {
                $user->update([
                    'kingschat_access_token' => $newTokens['access_token'],
                    'kingschat_refresh_token' => $newTokens['refresh_token'],
                    'kingschat_token_expires_at' => $newTokens['expires_at']
                ]);

                return $newTokens['access_token'];
            }
        }

        return null;
    }

    /**
     * Send a message to a user using their own access token
     *
     * @param \App\Models\User $user The user to send message to
     * @param string $message The message content
     * @return bool True if message was sent successfully
     */
    public function sendMessageToUser(\App\Models\User $user, string $message): bool
    {
        if (!$user->hasKingsChatConnected()) {
            return false;
        }

        // Use system token to send message to user
        return $this->sendMessage($user->kingschat_user_id, $message);
    }

    /**
     * Check if KingsChat service is properly configured
     *
     * @return bool True if service is configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->systemAccessToken) && !empty($this->systemUserId);
    }

    /**
     * Get the system user ID
     *
     * @return string The system user ID
     */
    public function getSystemUserId(): string
    {
        return $this->systemUserId;
    }
}
